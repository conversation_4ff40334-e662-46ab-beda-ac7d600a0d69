<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDGA Tournament Scraper</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header class="main-header">
            <div class="header-content">
                <div>
                    <h1>🏆 TOURNAMENTS</h1>
                    <p>Professional Disc Golf Association Live Events</p>
                </div>
            </div>
            <button id="update-btn">UPDATE</button>
            <span id="update-status"></span>
        </header>
        <main>
            <div id="events"></div>
        </main>
    </div>
    <!-- Modal for event details -->
    <div id="event-modal" class="modal-overlay" style="display:none;">
        <div class="modal-content">
            <button class="modal-close" id="modal-close-btn" aria-label="Close">&times;</button>
            <div id="modal-details"></div>
        </div>
    </div>
    <script>
    let eventData = [];
    async function fetchEvents() {
        const res = await fetch('/api/events');
        eventData = await res.json();
        renderEvents(eventData);
    }
    function renderEvents(events) {
        const now = new Date();

        // Group events by tier like PDGA Live site
        const tiers = {
            'Major': [],
            'A-Tier': [],
            'B-Tier': [],
            'C-Tier': []
        };

        events.forEach((ev, idx) => {
            const tier = ev.tier || 'A-Tier';
            if (tiers[tier]) {
                tiers[tier].push({event: ev, index: idx});
            } else {
                tiers['A-Tier'].push({event: ev, index: idx});
            }
        });

        let html = '';

        // Render each tier section (only if it has events)
        Object.keys(tiers).forEach(tierName => {
            if (tiers[tierName].length > 0) {
                html += `<div class="tier-section">`;
                html += `<h2 class="tier-header">${tierName}</h2>`;
                html += `<div class="tier-events">`;

                // Sort events within tier: ongoing first, then upcoming, then ended
                tiers[tierName].sort((a, b) => {
                    const statusA = getStatusType(a.event, now);
                    const statusB = getStatusType(b.event, now);

                    // Priority order: ongoing > upcoming > ended
                    const statusOrder = { 'ongoing': 0, 'upcoming': 1, 'ended': 2 };

                    if (statusOrder[statusA] !== statusOrder[statusB]) {
                        return statusOrder[statusA] - statusOrder[statusB];
                    }

                    // Within same status, sort by date
                    if (statusA === 'ended') {
                        return new Date(b.event.end) - new Date(a.event.end); // Most recent ended first
                    } else {
                        return new Date(a.event.start) - new Date(b.event.start); // Earliest start first
                    }
                });

                tiers[tierName].forEach(item => {
                    const status = getStatusType(item.event, now);
                    html += eventCard(item.event, status, item.index);
                });

                html += `</div></div>`;
            }
        });

        if (!html) html = '<p>No events found.</p>';
        document.getElementById('events').innerHTML = html;
        document.querySelectorAll('.event-card').forEach(card => {
            card.onclick = function() {
                const idx = this.getAttribute('data-idx');
                openModal(idx);
            };
        });
    }
    function getStatusType(ev, now) {
        const start = new Date(ev.start);
        const end = new Date(ev.end);

        // Debug: log the first few events to see what's happening
        if (Math.random() < 0.05) { // Only log ~5% of events to avoid spam
            console.log('Event:', ev.event_name);
            console.log('Now:', now.toISOString().split('T')[0]);
            console.log('Start:', ev.start);
            console.log('End:', ev.end);
            console.log('---');
        }

        if (now >= start && now <= end) return 'ongoing';
        if (now < start) return 'upcoming';
        return 'ended';
    }
    function eventCard(ev, statusType, idx) {
        const now = new Date();
        const start = new Date(ev.start);
        let status = '';
        if (statusType === 'ongoing') {
            status = `<span class=\"badge ongoing\">Ongoing</span>`;
        } else if (statusType === 'upcoming') {
            const days = Math.ceil((start - now) / (1000*60*60*24));
            status = `<span class=\"badge upcoming\">Upcoming in ${days}d</span>`;
        } else {
            status = `<span class=\"badge ended\">Ended</span>`;
        }
        // Use info from the info box if available
        const date = ev.dates ? ev.dates : '';
        const location = ev.location ? ev.location : '';
        const tier = ev.tier ? ev.tier : 'Unknown';
        return `
        <section class=\"event-card${statusType === 'ended' ? ' ended-card' : ''}\" tabindex=\"0\" data-idx=\"${idx}\">
            <div class=\"event-card-header\">
                <h2>${ev.event_name}</h2>
                <div class=\"event-status\">${status}</div>
            </div>
            <div class=\"event-meta\">
                <span>${date}</span>
                <span>📍 ${location}</span>
                <span class=\"tier-badge tier-${tier.toLowerCase().replace('-', '').replace(' ', '')}\">${tier}</span>
            </div>
        </section>
        `;
    }
    function openModal(idx) {
        const ev = eventData[idx];
        if (!ev) return;

        const modalDetails = document.getElementById('modal-details');

        if (ev.leaderboard_html) {
            // Event has leaderboard data
            modalDetails.innerHTML = ev.leaderboard_html;
            // Post-process: collapse all tables, remove unwanted columns, restyle
            setTimeout(() => {
                refineLeaderboardTables();
            }, 0);
        } else {
            // Event has no leaderboard data - show event info instead
            let content = `<div class="no-leaderboard-content">`;
            content += `<h2>${ev.event_name}</h2>`;

            if (ev.info_html) {
                content += `<div class="event-info">${ev.info_html}</div>`;
            } else {
                content += `<div class="event-basic-info">`;
                content += `<p><strong>Date:</strong> ${ev.dates}</p>`;
                content += `<p><strong>Location:</strong> 📍 ${ev.location}</p>`;
                if (ev.tier) {
                    content += `<p><strong>Tier:</strong> <span class="tier-badge tier-${ev.tier.toLowerCase().replace('-', '').replace(' ', '')}">${ev.tier}</span></p>`;
                }
                if (ev.website) {
                    content += `<p><strong>Website:</strong> <a href="${ev.website}" target="_blank">${ev.website}</a></p>`;
                }
                if (ev.event_url) {
                    content += `<p><strong>Tournament Page:</strong> <a href="${ev.event_url}" target="_blank">View on PDGA Live</a></p>`;
                }
                content += `</div>`;

                // Add divisions if available
                if (ev.divisions && ev.divisions.length > 0) {
                    content += `<div class="tournament-divisions">
                        <h3>Divisions</h3>
                        <div class="divisions-grid">`;

                    ev.divisions.forEach(divisionGroup => {
                        // Split the division group by newlines and filter out empty strings
                        const divisions = divisionGroup.split('\\n').filter(d => d.trim() && d.trim() !== 'LEADERS');
                        divisions.forEach(division => {
                            if (division.trim()) {
                                content += `<span class="division-badge">${division.trim()}</span>`;
                            }
                        });
                    });

                    content += `</div></div>`;
                }

                // Add rounds if available
                if (ev.rounds && ev.rounds.length > 0) {
                    const uniqueRounds = [...new Set(ev.rounds)].filter(r => r.trim());
                    if (uniqueRounds.length > 0) {
                        content += `<div class="tournament-rounds">
                            <h3>Rounds</h3>
                            <p>${uniqueRounds.length} round${uniqueRounds.length > 1 ? 's' : ''} available</p>
                        </div>`;
                    }
                }
            }

            // Check if it's a TBD event or upcoming event
            const now = new Date();
            const start = new Date(ev.start);
            const end = new Date(ev.end);

            if (ev.dates && ev.dates.toUpperCase().includes('TBD')) {
                content += `<div class="status-message tbd-message">
                    <p><strong>Status:</strong> Event details are still being finalized (TBD)</p>
                    <p>Leaderboard will be available once the event begins.</p>
                </div>`;
            } else if (now < start) {
                content += `<div class="status-message upcoming-message">
                    <p><strong>Status:</strong> Event has not started yet</p>
                    <p>Leaderboard will be available once the event begins.</p>
                </div>`;
            } else {
                content += `<div class="status-message no-data-message">
                    <p><strong>Status:</strong> No leaderboard data available</p>
                    <p>This event may not have published results or the data format is not supported.</p>
                </div>`;
            }

            content += `</div>`;
            modalDetails.innerHTML = content;
        }

        document.getElementById('event-modal').style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    function closeModal() {
        document.getElementById('event-modal').style.display = 'none';
        document.body.style.overflow = '';
        document.getElementById('modal-details').innerHTML = '';
    }
    function refineLeaderboardTables() {
        const modalDetails = document.getElementById('modal-details');
        // Find all leaderboard tables - avoid duplicates by being more specific
        // Priority order: details tables first (they have better division names), then direct tables
        const detailsTables = Array.from(modalDetails.querySelectorAll('div.leaderboard.singles details table'));
        const directTables = Array.from(modalDetails.querySelectorAll('div.leaderboard.singles > table'));

        // Use details tables if available, otherwise fall back to direct tables
        const allTables = detailsTables.length > 0 ? detailsTables : directTables;

        // Remove all content from modalDetails
        modalDetails.innerHTML = '';

        // For each leaderboard table, process and append
        allTables.forEach((table, i) => {
            // Remove all <a> tags in the table, keep only their text
            table.querySelectorAll('a').forEach(a => {
                const text = document.createTextNode(a.textContent);
                a.parentNode.replaceChild(text, a);
            });
            // Remove unwanted columns
            let ths = Array.from(table.querySelectorAll('thead th'));
            let unwanted = ['Current', 'Official', 'Verified', 'State'];
            let keepIdx = ths.map((th, i) => unwanted.indexOf(th.textContent.trim()) === -1 ? i : -1).filter(i => i !== -1);
            ths.forEach((th, i) => {
                if (keepIdx.indexOf(i) === -1) th.style.display = 'none';
            });
            table.querySelectorAll('tbody tr').forEach(tr => {
                let tds = Array.from(tr.children);
                tds.forEach((td, i) => {
                    if (keepIdx.indexOf(i) === -1) td.style.display = 'none';
                });
            });

            // Check if this is a live tournament (has Place column) or registration tournament (sort by Rating)
            let placeIdx = ths.findIndex(th => th.textContent.trim().toLowerCase() === 'place');
            let ratingIdx = ths.findIndex(th => th.textContent.trim().toLowerCase() === 'rating');

            if (placeIdx !== -1) {
                // Live tournament - sort by Place (lowest to highest)
                let rows = Array.from(table.querySelectorAll('tbody tr'));
                rows.sort((a, b) => {
                    let pa = parseInt((a.children[placeIdx]||{}).textContent) || 999;
                    let pb = parseInt((b.children[placeIdx]||{}).textContent) || 999;
                    return pa - pb;
                });
                let tbody = table.querySelector('tbody');
                rows.forEach(row => tbody.appendChild(row));
            } else if (ratingIdx !== -1) {
                // Registration tournament - sort by Rating (highest to lowest)
                let rows = Array.from(table.querySelectorAll('tbody tr'));
                rows.sort((a, b) => {
                    let ra = parseInt((a.children[ratingIdx]||{}).textContent) || 0;
                    let rb = parseInt((b.children[ratingIdx]||{}).textContent) || 0;
                    return rb - ra;
                });
                let tbody = table.querySelector('tbody');
                rows.forEach(row => tbody.appendChild(row));
            }
            table.classList.add('modern-leaderboard');

            // Find the division name - check multiple possible locations
            let headerText = '';

            // Method 1: Check if table is inside a details element with summary h3
            const detailsParent = table.closest('details');
            if (detailsParent) {
                const summaryH3 = detailsParent.querySelector('summary h3');
                if (summaryH3) {
                    headerText = summaryH3.textContent.trim();
                }
            }

            // Method 2: Look for h3 with class 'division' in the leaderboard container
            if (!headerText) {
                const leaderboardDiv = table.closest('div.leaderboard.singles');
                if (leaderboardDiv) {
                    const divisionH3 = leaderboardDiv.querySelector('h3.division');
                    if (divisionH3) {
                        headerText = divisionH3.textContent.trim();
                    }
                }
            }

            // Method 3: Look for any h3 or h2 in the leaderboard container
            if (!headerText) {
                const leaderboardDiv = table.closest('div.leaderboard.singles');
                if (leaderboardDiv) {
                    const anyHeader = leaderboardDiv.querySelector('h3, h2, .division-header');
                    if (anyHeader) {
                        headerText = anyHeader.textContent.trim();
                    }
                }
            }

            // Method 4: Check previous siblings (original method as fallback)
            if (!headerText) {
                let prev = table.previousSibling;
                while (prev) {
                    if (prev.nodeType === 1 && (prev.matches('h3') || prev.matches('h2') || prev.classList.contains('division-header'))) {
                        headerText = prev.textContent.trim();
                        break;
                    }
                    prev = prev.previousSibling;
                }
            }

            const headerBtn = document.createElement('button');
            headerBtn.type = 'button';
            headerBtn.className = 'modern-leaderboard-header';
            headerBtn.textContent = headerText || `Division ${i+1}`;
            headerBtn.setAttribute('aria-expanded', i === 0 ? 'true' : 'false');
            // Collapse all but the first table by default
            table.style.display = i === 0 ? '' : 'none';
            headerBtn.onclick = function() {
                const expanded = table.style.display !== 'none';
                table.style.display = expanded ? 'none' : '';
                headerBtn.setAttribute('aria-expanded', !expanded ? 'true' : 'false');
            };
            modalDetails.appendChild(headerBtn);
            modalDetails.appendChild(table);
        });
    }
    document.getElementById('update-btn').onclick = async function() {
        const status = document.getElementById('update-status');
        status.textContent = 'Updating...';
        this.disabled = true;
        await fetch('/api/update', {method: 'POST'});
        await fetchEvents();
        status.textContent = 'Updated!';
        setTimeout(() => { status.textContent = ''; }, 2000);
        this.disabled = false;
    };
    document.getElementById('modal-close-btn').onclick = closeModal;
    document.getElementById('event-modal').onclick = function(e) {
        if (e.target === this) closeModal();
    };
    fetchEvents();
    </script>
</body>
</html> 