#!/usr/bin/env python3
"""
PDGA Live Events Scraper - Replacement for scraper.py
Scrapes tournament data from PDGA Live Events page and saves to JSON
"""

import json
import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

DATA_FILE = "data.json"

def setup_driver():
    """Setup Chrome driver with appropriate options"""
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error setting up Chrome driver: {e}")
        print("Make sure Chrome and ChromeDriver are installed")
        return None

def parse_date_range(date_str):
    """Parse date range like 'Sat-Sun, Jul 26-27, 2024' into start/end dates"""
    try:
        from datetime import datetime
        current_year = datetime.now().year

        # Extract the date part after the comma
        if ',' in date_str:
            date_part = date_str.split(',', 1)[1].strip()
        else:
            date_part = date_str.strip()

        # Handle different date formats
        if '-' in date_part:
            # Try to parse ranges like "Jul 26-27, 2024" or "Jul 26-27"
            parts = date_part.split()
            if len(parts) >= 2:
                month = parts[0]

                # Check if year is specified
                if len(parts) >= 3 and parts[-1].isdigit():
                    year = int(parts[-1])
                    day_range = parts[1].rstrip(',')
                else:
                    year = current_year
                    day_range = parts[-1].rstrip(',')

                if '-' in day_range:
                    start_day, end_day = day_range.split('-')
                    start_date = f"{year}-{month_to_num(month):02d}-{int(start_day):02d}"
                    end_date = f"{year}-{month_to_num(month):02d}-{int(end_day):02d}"
                    return start_date, end_date
                else:
                    # Single day
                    day = int(day_range.rstrip(','))
                    date = f"{year}-{month_to_num(month):02d}-{day:02d}"
                    return date, date

        # Fallback to current date
        current_date = datetime.now().strftime("%Y-%m-%d")
        return current_date, current_date
    except:
        from datetime import datetime
        current_date = datetime.now().strftime("%Y-%m-%d")
        return current_date, current_date  # Default fallback

def month_to_num(month_str):
    """Convert month abbreviation to number"""
    months = {
        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
    }
    return months.get(month_str, 12)

def extract_tournament_data(driver):
    """Extract tournament data using the tier structure from PDGA Live Events"""
    tournaments = []

    try:
        # Wait for the events container to load
        print("Waiting for events container...")
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.events div.app-container"))
        )

        # Find tier sections using the CSS path provided
        tier_sections = driver.find_elements(By.CSS_SELECTOR, "div.events div.app-container div.d-flex.flex-wrap.gap-5 div.w-100")
        print(f"Found {len(tier_sections)} tier sections")

        for tier_section in tier_sections:
            try:
                # Extract tier name from h3 element
                tier_name = "A-Tier"  # Default
                try:
                    tier_header = tier_section.find_element(By.CSS_SELECTOR, "h3.px-3")
                    if tier_header and tier_header.text.strip():
                        tier_name = tier_header.text.strip()
                        print(f"Processing tier: {tier_name}")
                except:
                    print("No tier header found in this section, skipping...")
                    continue

                # Find tournament event links within this tier section using the specific CSS path
                tournament_links = tier_section.find_elements(By.CSS_SELECTOR, "a.list-event.p-3")

                print(f"Found {len(tournament_links)} tournament links in {tier_name}")

                processed_names = set()  # Avoid duplicates within this tier

                for link in tournament_links:
                    try:
                        # Extract tournament name from the link
                        tournament_name = ""
                        try:
                            # Look for tournament name in various possible selectors within the link
                            name_selectors = ["h4", "h3", ".tournament-name", ".event-name"]
                            for selector in name_selectors:
                                try:
                                    name_elem = link.find_element(By.CSS_SELECTOR, selector)
                                    if name_elem and name_elem.text.strip():
                                        tournament_name = name_elem.text.strip()
                                        break
                                except:
                                    continue

                            # If no specific selector worked, try getting the first meaningful text
                            if not tournament_name:
                                link_text = link.text.strip()
                                lines = [line.strip() for line in link_text.split('\n') if line.strip()]
                                if lines:
                                    tournament_name = lines[0]
                        except:
                            continue

                        if not tournament_name or len(tournament_name) < 10:
                            continue

                        # Skip if this is the tier header itself or other non-tournament text
                        if (tournament_name == tier_name or
                            tournament_name.lower() in ['loading', 'error', 'menu', 'nav'] or
                            not any(keyword in tournament_name.lower() for keyword in ['open', 'championship', 'classic', 'cup', 'series', 'tour', 'disc', 'golf'])):
                            continue

                        # Skip duplicates
                        if tournament_name in processed_names:
                            continue

                        # Extract location using multiple approaches
                        location = "Unknown"

                        # Try the specific CSS path you provided first
                        try:
                            location_elem = link.find_element(By.CSS_SELECTOR, "div.event-meta div.label-2.text-muted.d-flex.flex-nowrap.text-nowrap.align-items-center.gap-3 div.d-flex.align-items-center div.event-info-text")
                            if location_elem and location_elem.text.strip():
                                location = location_elem.text.strip()
                                print(f"Found location via CSS path: {location}")
                        except Exception as e:
                            print(f"CSS path failed for {tournament_name}: {e}")
                            pass

                        # If that didn't work, try other location selectors
                        if location == "Unknown":
                            location_selectors = [
                                "div.event-info-text",
                                "div.event-meta div.event-info-text",
                                "div.label-2 div.event-info-text",
                                ".location", ".venue", ".city", ".state"
                            ]

                            for selector in location_selectors:
                                try:
                                    loc_elem = link.find_element(By.CSS_SELECTOR, selector)
                                    if loc_elem and loc_elem.text.strip():
                                        location = loc_elem.text.strip()
                                        break
                                except:
                                    continue

                        # Fallback: parse from the full link text
                        if location == "Unknown":
                            try:
                                link_text = link.text.strip()
                                lines = [line.strip() for line in link_text.split('\n') if line.strip()]

                                # Look for lines that look like locations
                                for line in lines[1:]:  # Skip the tournament name
                                    if (len(line) > 2 and
                                        not any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']) and
                                        not line.isdigit() and
                                        (any(indicator in line for indicator in [',', 'Course', 'Park', 'Club', 'Center', 'DGC', 'Disc Golf']) or
                                         len(line) == 2 and line.isupper())):  # State abbreviation
                                        location = line
                                        break
                            except:
                                pass

                        # Extract date information
                        date_info = ""
                        try:
                            link_text = link.text.strip()
                            lines = [line.strip() for line in link_text.split('\n') if line.strip()]
                            for line in lines:
                                if any(month in line for month in ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']):
                                    date_info = line
                                    break
                        except:
                            continue

                        if not date_info:
                            continue

                        # Parse dates
                        start_date, end_date = parse_date_range(date_info)

                        if not start_date or not end_date:
                            continue

                        # Extract tournament URL from the link
                        tournament_url = ""
                        try:
                            href = link.get_attribute('href')
                            if href:
                                tournament_url = href
                        except:
                            pass

                        # Extract year from start_date
                        year = int(start_date.split('-')[0]) if start_date else datetime.now().year

                        tournament = {
                            "event_name": tournament_name,
                            "dates": date_info,
                            "start": start_date,
                            "end": end_date,
                            "location": location,
                            "tier": tier_name,  # Use the tier from the section header
                            "year": year,
                            "event_url": tournament_url,
                            "competitors": "TBD",
                            "website": None,
                            "info_html": None,
                            "leaderboard_html": None
                        }

                        tournaments.append(tournament)
                        processed_names.add(tournament_name)

                    except Exception as e:
                        continue  # Skip problematic cards

            except Exception as e:
                print(f"Error processing tier section: {e}")
                continue

        return tournaments

    except Exception as e:
        print(f"Error extracting tournament data: {e}")
        return []

def scrape_tournament_details(driver, tournament_url):
    """Scrape individual tournament page for division and round data"""
    try:
        print(f"Scraping tournament details from: {tournament_url}")
        driver.get(tournament_url)

        # Wait for the tournament page to load
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "div.event div div div.app-container"))
        )

        # Find divisions and rounds data
        divisions = []
        rounds = []

        # Look for division information
        try:
            division_elements = driver.find_elements(By.CSS_SELECTOR, "div.app-container div[class*='division'], div.app-container div[class*='class']")
            for div_elem in division_elements:
                div_text = div_elem.text.strip()
                if div_text and len(div_text) > 1:
                    divisions.append(div_text)
        except:
            pass

        # Look for round information
        try:
            round_elements = driver.find_elements(By.CSS_SELECTOR, "div.app-container div[class*='round'], div.app-container button[class*='round']")
            for round_elem in round_elements:
                round_text = round_elem.text.strip()
                if round_text and ('round' in round_text.lower() or round_text.startswith('R')):
                    rounds.append(round_text)
        except:
            pass

        # Look for leaderboard/score buttons
        score_buttons = []
        try:
            button_elements = driver.find_elements(By.CSS_SELECTOR, "div.app-container button, div.app-container a[class*='button']")
            for button in button_elements:
                button_text = button.text.strip()
                if button_text and any(keyword in button_text.lower() for keyword in ['score', 'leaderboard', 'results']):
                    score_buttons.append({
                        'text': button_text,
                        'url': button.get_attribute('href') or button.get_attribute('onclick') or ''
                    })
        except:
            pass

        return {
            'divisions': divisions,
            'rounds': rounds,
            'score_buttons': score_buttons
        }

    except Exception as e:
        print(f"Error scraping tournament details: {e}")
        return {
            'divisions': [],
            'rounds': [],
            'score_buttons': []
        }

def scrape_tournaments(include_details=False, max_details=5):
    """Main scraping function"""
    driver = setup_driver()
    if not driver:
        return []

    try:
        print("Loading PDGA Live Events page...")
        driver.get("https://www.pdga.com/live/events")

        # Wait for content to load
        print("Waiting for tournament content to load...")
        wait = WebDriverWait(driver, 30)
        wait.until(
            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), '2025')]"))
        )

        print("Content loaded, waiting for full render...")
        time.sleep(10)

        # Extract tournament data
        tournaments = extract_tournament_data(driver)

        print(f"Successfully extracted {len(tournaments)} tournaments")

        # Optionally scrape tournament details
        if include_details and tournaments:
            print(f"Scraping details for up to {max_details} tournaments...")
            details_scraped = 0

            for tournament in tournaments:
                if details_scraped >= max_details:
                    break

                if tournament.get('event_url'):
                    try:
                        details = scrape_tournament_details(driver, tournament['event_url'])
                        tournament['divisions'] = details['divisions']
                        tournament['rounds'] = details['rounds']
                        tournament['score_buttons'] = details['score_buttons']
                        details_scraped += 1
                        print(f"Scraped details for: {tournament['event_name']}")
                        print(f"  Divisions: {details['divisions']}")
                        print(f"  Rounds: {details['rounds']}")
                        print(f"  Score buttons: {len(details['score_buttons'])}")

                        # Small delay between requests
                        time.sleep(2)

                    except Exception as e:
                        print(f"Failed to scrape details for {tournament['event_name']}: {e}")
                        continue

            print(f"Scraped details for {details_scraped} tournaments")

        return tournaments
        
    except TimeoutException:
        print("Timeout waiting for page to load")
        return []
    except Exception as e:
        print(f"Error scraping live events: {e}")
        return []
    finally:
        driver.quit()

def main():
    """Main function"""
    print("Starting PDGA Live Events scraper...")

    # Check if we should include tournament details
    import sys
    include_details = len(sys.argv) > 1 and sys.argv[1] == '--details'

    tournaments = scrape_tournaments(include_details=include_details, max_details=5)
    
    if tournaments:
        # Save to JSON file
        with open(DATA_FILE, "w", encoding="utf-8") as f:
            json.dump(tournaments, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(tournaments)} tournaments to {DATA_FILE}")
        
        # Show tier breakdown
        tier_counts = {}
        for t in tournaments:
            tier = t.get('tier', 'Unknown')
            tier_counts[tier] = tier_counts.get(tier, 0) + 1
        
        print("\nTier breakdown:")
        for tier, count in tier_counts.items():
            print(f"  {tier}: {count}")
            
        print("\nScraping completed successfully!")
    else:
        print("No tournaments found or scraping failed")

if __name__ == "__main__":
    main()
