#!/usr/bin/env python3
"""
PDGA Tournament Scraper Web Application

Flask web application that serves tournament data scraped from PDGA Live Events.
Features a dark theme interface with tier-organized tournament cards and modal details.

Usage:
    python app.py

Then navigate to http://127.0.0.1:5000 in your browser.
"""

from flask import Flask, jsonify, send_from_directory, render_template
import subprocess
import json
import os

app = Flask(__name__, static_folder='static', template_folder='templates')

DATA_FILE = 'data.json'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/events')
def get_events():
    if not os.path.exists(DATA_FILE):
        return jsonify([])
    with open(DATA_FILE, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return jsonify(data)

@app.route('/api/update', methods=['POST'])
def update_events():
    # Run the scraper
    subprocess.run(['python', 'scraper.py'])
    # Return updated data
    if not os.path.exists(DATA_FILE):
        return jsonify([])
    with open(DATA_FILE, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return jsonify(data)

# Serve static files (CSS, JS, etc.)
@app.route('/static/<path:path>')
def send_static(path):
    return send_from_directory('static', path)

if __name__ == '__main__':
    app.run(debug=True) 