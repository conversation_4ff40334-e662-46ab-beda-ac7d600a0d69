#!/usr/bin/env python3
"""
PDGA Tournament Scraper Utility

Utility script to run different PDGA tournament scrapers.
- New scraper: Uses Selenium to scrape PDGA Live Events (recommended)
- Old scraper: Legacy scraper kept as backup

Usage:
    python run_scraper.py new    # Use new live events scraper (default)
    python run_scraper.py old    # Use legacy scraper
"""

import sys
import os

def run_old_scraper():
    """Run the original PDGA scraper"""
    print("Running original PDGA scraper (scraper.py)...")
    os.system("python scraper.py")

def run_new_scraper():
    """Run the new PDGA Live Events scraper"""
    print("Running new PDGA Live Events scraper (scraper_live.py)...")
    os.system("python scraper_live.py")

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] == "old":
            run_old_scraper()
        elif sys.argv[1] == "new" or sys.argv[1] == "live":
            run_new_scraper()
        else:
            print("Usage: python run_scraper.py [old|new|live]")
            print("  old  - Run original PDGA scraper")
            print("  new  - Run new PDGA Live Events scraper")
            print("  live - Run new PDGA Live Events scraper")
    else:
        # Default to new scraper
        print("No argument provided, running new PDGA Live Events scraper by default...")
        run_new_scraper()

if __name__ == "__main__":
    main()
