# PDGA Tournament Scraper

A modern web application that scrapes and displays Professional Disc Golf Association (PDGA) tournament data with a dark theme interface matching the PDGA Live Events style.

## Features

- **Real-time Tournament Data**: Scrapes live tournament information from PDGA Live Events
- **Tier Organization**: Tournaments organized by tier (Major, A-Tier, B-Tier, C-Tier)
- **Dark Theme**: Modern dark interface matching PDGA Live Events style
- **Compact Cards**: Small, efficient tournament cards for easy browsing
- **Smart Sorting**: Events sorted by status (ongoing → upcoming → ended) within each tier
- **Modal Details**: Click any tournament card to view detailed leaderboard information
- **Responsive Design**: Works on desktop and mobile devices

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install flask beautifulsoup4 requests selenium
   ```

2. **Run the Scraper**:
   ```bash
   python run_scraper.py new
   ```

3. **Start the Web App**:
   ```bash
   python app.py
   ```

4. **Open in Browser**:
   Navigate to `http://127.0.0.1:5000`

## Project Structure

```
pdgascraper/
├── app.py              # Flask web application
├── scraper_live.py     # Main scraper using Selenium
├── scraper.py          # Legacy scraper (backup)
├── run_scraper.py      # Utility script for running scrapers
├── data.json           # Tournament data storage
├── static/
│   └── style.css       # Dark theme styling
└── templates/
    └── index.html      # Main web interface
```

## Usage

### Updating Tournament Data
```bash
# Use the new live events scraper (recommended)
python run_scraper.py new

# Use the legacy scraper (backup)
python run_scraper.py old
```

### Running the Web Interface
```bash
python app.py
```
The web interface will be available at `http://127.0.0.1:5000`

## Tournament Tiers

- **Major**: World Championships, USDGC, European Open
- **A-Tier**: Elite Series events and major tournaments (default)
- **B-Tier**: Regional events, weekly leagues, flex rounds
- **C-Tier**: Local events, charity tournaments, amateur-only events

## Technical Details

- **Backend**: Flask (Python)
- **Scraping**: Selenium WebDriver for JavaScript-rendered content
- **Parsing**: BeautifulSoup4 for HTML processing
- **Frontend**: Vanilla JavaScript with modern CSS
- **Data**: JSON storage for tournament information

## Browser Requirements

Chrome browser is required for the Selenium WebDriver. The scraper will automatically download and manage the ChromeDriver.

## License

This project is for educational and personal use only. Please respect PDGA's terms of service when using this scraper.
