import requests
from bs4 import BeautifulSoup
import json
from datetime import datetime, timedelta
import re
import time

PDGA_URL = "https://www.pdga.com/major-disc-golf-events"
DATA_FILE = "data.json"
BASE_URL = "https://www.pdga.com"

# Helper to parse date ranges like 'July 26 - Aug 3' or 'Aug 8-15'
def parse_event_dates(date_str, year):
    months = {
        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
    }
    # e.g. 'July 26 - Aug 3'
    match = re.match(r"([A-Za-z]+) (\d+)[ -]+([A-Za-z]+)? ?(\d+)?", date_str)
    if match:
        m1, d1, m2, d2 = match.groups()
        m1 = m1[:3]
        start_month = months[m1]
        start_day = int(d1)
        if m2 and d2:
            m2 = m2[:3]
            end_month = months[m2]
            end_day = int(d2)
        else:
            end_month = start_month
            end_day = int(d1)
        start = datetime(year, start_month, start_day)
        end = datetime(year, end_month, end_day)
        return start, end
    # e.g. 'April 2-5'
    match = re.match(r"([A-Za-z]+) (\d+)-(\d+)", date_str)
    if match:
        m, d1, d2 = match.groups()
        m = m[:3]
        month = months[m]
        start = datetime(year, month, int(d1))
        end = datetime(year, month, int(d2))
        return start, end
    # fallback: just use today
    today = datetime.now()
    return today, today

def get_event_details(event_url):
    # Step 1: Go to the event detail page
    try:
        resp = requests.get(event_url, timeout=10)
        soup = BeautifulSoup(resp.text, 'html.parser')
        # Step 2: Find all buttons to the actual event page (a.button.box2)
        event_btns = soup.select('a.button.box2')
        actual_event_url = None
        for btn in event_btns:
            href = btn.get('href', '')
            # Look for various event URL patterns, including live tournament pages
            if any(pattern in href for pattern in ['/tour/event/', '/events/', '/tournament/', '/tour/live']):
                actual_event_url = href
                break

        # If no button found, try to find any link that might be an event page
        if not actual_event_url:
            all_links = soup.find_all('a', href=True)
            for link in all_links:
                href = link.get('href', '')
                # Look for event-related URLs
                if any(pattern in href for pattern in ['/tour/event/', '/events/', '/tournament/', '/tour/live']):
                    actual_event_url = href
                    break

        if not actual_event_url:
            return None, None, None, None, None
        if not actual_event_url.startswith('http'):
            actual_event_url = BASE_URL + actual_event_url

        # Convert live tournament URLs to static event URLs
        if '/tour/live?TournID=' in actual_event_url:
            import re
            match = re.search(r'TournID=(\d+)', actual_event_url)
            if match:
                tourn_id = match.group(1)
                actual_event_url = f"{BASE_URL}/tour/event/{tourn_id}"

        # Step 3: Go to the actual event page
        resp2 = requests.get(actual_event_url, timeout=10)
        soup2 = BeautifulSoup(resp2.text, 'html.parser')
        # Step 4: Find the info box and leaderboard
        info_div = soup2.select_one('div.pane-tournament-event-info > div.pane-content')

        # Try different leaderboard selectors - events can have different modes
        leaderboard_div = None
        leaderboard_selectors = [
            'div.leaderboard.singles.mode-live',    # Live/completed events
            'div.leaderboard.singles.mode-reg',     # Registration mode events
            'div.leaderboard.singles.mode-normal',  # Normal mode events
            'div.leaderboard.singles',              # Fallback - any singles leaderboard
            'div.leaderboard'                       # Last resort - any leaderboard
        ]

        for selector in leaderboard_selectors:
            leaderboard_div = soup2.select_one(selector)
            if leaderboard_div:
                break

        info_html = str(info_div) if info_div else None
        leaderboard_html = str(leaderboard_div) if leaderboard_div else None
        # Parse date, location, website from info_html
        date, location, website = None, None, None
        if info_div:
            # Try to extract date, location, website from the info box
            text = info_div.get_text("\n", strip=True)
            # Date: look for a line starting with 'Date:'
            for line in text.split('\n'):
                if line.lower().startswith('date:'):
                    date = line.split(':', 1)[-1].strip()
                if line.lower().startswith('location:'):
                    location = line.split(':', 1)[-1].strip()
            # Website: look for the first <a> with 'Website:' in the previous text
            for a in info_div.find_all('a', href=True):
                if 'website' in a.get_text(strip=True).lower():
                    website = a['href']
                    break
            # Fallback: just use the first link
            if not website:
                a = info_div.find('a', href=True)
                if a:
                    website = a['href']
        return info_html, leaderboard_html, date, location, website
    except Exception as e:
        print(f"Error scraping event details for {event_url}: {e}")
        return None, None, None, None, None

def scrape_and_save():
    resp = requests.get(PDGA_URL)
    soup = BeautifulSoup(resp.text, 'html.parser')
    events = []
    now = datetime.now()
    for table in soup.find_all('table'):
        prev = table.find_previous(['h2', 'h3'])
        if not prev or not prev.text.strip().isdigit():
            continue
        year = int(prev.text.strip())
        for row in table.find_all('tr')[1:]:
            cols = row.find_all(['td', 'th'])
            if len(cols) < 4:
                continue
            date_str = cols[0].get_text(strip=True)
            event_name = cols[1].get_text(strip=True)
            competitors = cols[2].get_text(strip=True)
            location = cols[3].get_text(strip=True)
            # Skip if date is missing, but allow TBD events
            if not date_str:
                continue

            # Handle TBD events differently
            is_tbd = 'TBD' in date_str.upper()
            if is_tbd:
                # For TBD events, use a future date so they appear in upcoming
                start = datetime(year, 12, 31)
                end = datetime(year, 12, 31)
            else:
                start, end = parse_event_dates(date_str, year)
                # Remove the 30-day limit to show all past events
                # if end < now - timedelta(days=30):
                #     continue
            # Find event link
            event_link_tag = cols[1].find('a')
            event_url = None
            if event_link_tag and event_link_tag.get('href'):
                event_url = event_link_tag['href']
                if not event_url.startswith('http'):
                    event_url = BASE_URL + event_url
            info_html, leaderboard_html, real_date, real_location, real_website = None, None, None, None, None
            if event_url:
                info_html, leaderboard_html, real_date, real_location, real_website = get_event_details(event_url)
                time.sleep(0.5)  # Be polite to the server
            events.append({
                'event_name': event_name,
                'dates': real_date or date_str,
                'start': start.strftime('%Y-%m-%d'),
                'end': end.strftime('%Y-%m-%d'),
                'competitors': competitors,
                'location': real_location or location,
                'year': year,
                'event_url': event_url,
                'website': real_website,
                'info_html': info_html,
                'leaderboard_html': leaderboard_html
            })
    events.sort(key=lambda e: (e['start'] > now.strftime('%Y-%m-%d'), e['start']))
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(events, f, indent=2, ensure_ascii=False)

if __name__ == "__main__":
    scrape_and_save() 