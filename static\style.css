/* PDGA Live Dark Theme */
body {
    background: #1a1a1a;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    margin: 0;
    color: #ffffff;
    min-height: 100vh;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: #1a1a1a;
    padding: 2rem;
}
.main-header {
    background: #2a2a2a;
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid #333;
    color: #fff;
}
.header-content {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    margin-bottom: 0.7rem;
}
.main-header h1 {
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    font-size: 2rem;
    margin: 0 0 0.2rem 0;
    color: #fff;
    letter-spacing: 0.5px;
    font-weight: 700;
    text-transform: uppercase;
}
.main-header p {
    margin: 0;
    color: #b8c1ec;
    font-size: 1.08rem;
    font-weight: 400;
}
#update-btn {
    background: linear-gradient(90deg, #0066cc 0%, #0052a3 100%);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.7em 2.2em;
    font-size: 1.02rem;
    font-family: '<PERSON>', 'Open Sans', Arial, sans-serif;
    font-weight: 700;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(35,41,70,0.10);
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.13s;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
    display: inline-block;
    letter-spacing: 1.5px;
    text-transform: uppercase;
}
#update-btn:hover:not(:disabled) {
    background: linear-gradient(90deg, #0052a3 0%, #0066cc 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(35,41,70,0.13);
    transform: scale(1.045);
}
#update-btn:disabled {
    background: #b0b0b0;
    color: #fff;
    cursor: not-allowed;
}
#update-status {
    margin-left: 1em;
    color: #fff;
    font-weight: 600;
    font-size: 1.08rem;
    letter-spacing: 1px;
}
/* Tier sections like PDGA Live */
.tier-section {
    margin-bottom: 2rem;
}

.tier-header {
    font-size: 1.4em;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 1rem 0;
    padding: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tier-events {
    display: grid;
    gap: 1rem;
}

.section-header {
    font-size: 1.05rem;
    color: #ffffff;
    margin: 2.2rem 0 1.1rem 0;
    font-weight: 700;
    letter-spacing: 2.5px;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    text-transform: uppercase;
}
.event-card {
    background: #2a2a2a;
    border: 1px solid #333;
    border-radius: 6px;
    margin-bottom: 0.4rem;
    padding: 0.5rem 0.75rem;
    transition: background-color 0.2s, border-color 0.2s;
    outline: none;
    min-width: 0;
    color: #ffffff;
    cursor: pointer;
}
.event-card:focus, .event-card:hover {
    background: #333;
    border-color: #555;
}
.event-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.3em;
    gap: 0.5em;
}
.event-card h2 {
    margin: 0;
    font-size: 0.9rem;
    color: #ffffff;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    font-weight: 600;
    letter-spacing: 0.3px;
    line-height: 1.2;
}
.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75em;
    font-size: 0.75rem;
    color: #ccc;
    margin-bottom: 0.1em;
    margin-top: 0.1em;
    font-family: 'Open Sans', Arial, sans-serif;
    font-weight: 400;
}
.event-status {
    margin-top: 0.1em;
    display: flex;
    align-items: center;
    gap: 0.5em;
}
.badge {
    display: inline-block;
    font-size: 0.85em;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    font-weight: 600;
    padding: 0.13em 0.7em;
    border-radius: 3px;
    letter-spacing: 1px;
    box-shadow: 0 1px 2px rgba(35,41,70,0.04);
    text-transform: uppercase;
}
.badge.ongoing {
    background: #2ecc40;
    color: #fff;
}
.badge.upcoming {
    background: #0066cc;
    color: #fff;
}
.badge.ended {
    background: #b0b0b0;
    color: #fff;
}

/* Tier badges */
.tier-badge {
    display: inline-block;
    font-size: 0.65em;
    font-family: 'Inter', 'Open Sans', Arial, sans-serif;
    font-weight: 700;
    padding: 0.15em 0.4em;
    border-radius: 3px;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    border: 1px solid;
}

.tier-major {
    background: #ff4444;
    color: #fff;
    border-color: #cc3333;
}

.tier-atier {
    background: #00cc66;
    color: #fff;
    border-color: #00aa55;
}

.tier-btier {
    background: #3399ff;
    color: #fff;
    border-color: #2288ee;
}

.tier-ctier {
    background: #ffaa00;
    color: #fff;
    border-color: #ee9900;
}

.tier-unknown {
    background: #666;
    color: #fff;
    border-color: #555;
}
.ended-card {
    opacity: 0.7;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    color: #aaaaaa;
}
/* Tournament details styles */
.tournament-divisions {
    margin-top: 1rem;
    padding: 1rem;
    background: #333;
    border-radius: 6px;
}

.tournament-divisions h3 {
    margin: 0 0 0.75rem 0;
    color: #ffffff;
    font-size: 1.1rem;
}

.divisions-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.division-badge {
    background: #4a90e2;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.tournament-rounds {
    margin-top: 1rem;
    padding: 1rem;
    background: #333;
    border-radius: 6px;
}

.tournament-rounds h3 {
    margin: 0 0 0.5rem 0;
    color: #ffffff;
    font-size: 1.1rem;
}

.tournament-rounds p {
    margin: 0;
    color: #cccccc;
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.7);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}
.modal-content {
    background: #2a2a2a;
    border: 1px solid #333;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.5);
    max-width: 600px;
    width: 95vw;
    max-height: 80vh;
    overflow-y: auto;
    padding: 2.2rem 2rem 1.5rem 2rem;
    position: relative;
    animation: modalIn 0.18s cubic-bezier(.4,1.6,.6,1) 1;
    color: #ffffff;
}
@keyframes modalIn {
    from { transform: translateY(40px) scale(0.98); opacity: 0; }
    to   { transform: translateY(0) scale(1); opacity: 1; }
}
.modal-close {
    position: absolute;
    top: 1.1rem;
    right: 1.1rem;
    background: none;
    border: none;
    font-size: 2rem;
    color: #ffffff;
    cursor: pointer;
    font-weight: 700;
    line-height: 1;
    transition: color 0.15s;
    z-index: 10;
}
.modal-close:hover {
    color: #00cc66;
}
#modal-details {
    font-family: 'Open Sans', Arial, sans-serif;
    color: #ffffff;
    font-size: 1.01rem;
    line-height: 1.6;
    margin-top: 0.5rem;
}
/* Modern leaderboard table styles */
.modern-leaderboard {
    width: 100%;
    border-collapse: collapse;
    margin: 1.2em 0 2em 0;
    font-size: 1rem;
    background: #fff;
    box-shadow: 0 1px 6px rgba(35,41,70,0.04);
}
.modern-leaderboard th, .modern-leaderboard td {
    padding: 0.55em 1em;
    border: 1px solid #e3e6ee;
    text-align: left;
}
.modern-leaderboard th {
    background: #f4f6fa;
    color: #232946;
    font-weight: 700;
    font-size: 1.01em;
    letter-spacing: 0.5px;
}
.modern-leaderboard tr:nth-child(even) td {
    background: #f8f9fb;
}
.modern-leaderboard tr:hover td {
    background: #eaf2fb;
}
.modern-leaderboard-header {
    background: #f4f6fa;
    color: #232946;
    font-size: 1.08em;
    font-weight: 700;
    margin: 1.5em 0 0.2em 0;
    padding: 0.7em 1em 0.5em 1em;
    border-radius: 5px 5px 0 0;
    border: 1px solid #e3e6ee;
    border-bottom: none;
    letter-spacing: 1px;
    cursor: pointer;
    width: 100%;
    text-align: left;
    outline: none;
    transition: background 0.18s, box-shadow 0.18s;
    display: flex;
    align-items: center;
    gap: 0.5em;
    position: relative;
}
.modern-leaderboard-header:hover, .modern-leaderboard-header:focus {
    background: #eaf2fb;
    box-shadow: 0 2px 8px rgba(35,41,70,0.07);
}
.modern-leaderboard-header::after {
    content: '';
    display: inline-block;
    margin-left: auto;
    border: solid #232946;
    border-width: 0 2.5px 2.5px 0;
    padding: 4px;
    transform: rotate(45deg);
    transition: transform 0.18s;
}
.modern-leaderboard-header[aria-expanded="true"]::after {
    transform: rotate(-135deg);
}
button.modern-leaderboard-header {
    border: none;
    background: #f4f6fa;
}
/* Collapsible registration section */
.registration h2, .registration h3, .registration .division-header {
    background: #f4f6fa;
    color: #232946;
    font-size: 1.08em;
    font-weight: 700;
    margin: 0;
    padding: 0.7em 1em;
    border-radius: 5px 5px 0 0;
    border: 1px solid #e3e6ee;
    border-bottom: none;
    transition: background 0.18s;
}
.registration h2:hover, .registration h3:hover, .registration .division-header:hover {
    background: #eaf2fb;
}
.registration table {
    border-radius: 0 0 5px 5px;
    border-top: none;
}

/* No leaderboard content styles */
.no-leaderboard-content {
    text-align: center;
    padding: 1rem;
}
.no-leaderboard-content h2 {
    color: #232946;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}
.event-info, .event-basic-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: left;
}
.event-basic-info p {
    margin: 0.5rem 0;
    color: #555;
}
.status-message {
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: left;
}
.tbd-message {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}
.upcoming-message {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}
.no-data-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}
.status-message p {
    margin: 0.5rem 0;
}
@media (max-width: 600px) {
    .container {
        padding: 1rem 0.5rem;
    }
    .main-header {
        padding: 1.1rem 0.7rem 1rem 0.7rem;
    }
    .header-content {
        flex-direction: column;
        gap: 0.5rem;
    }
    .event-meta {
        flex-direction: column;
        gap: 0.5em;
    }
    .event-card {
        padding: 1.1rem 0.7rem 0.9rem 0.7rem;
    }
    .modal-content {
        padding: 1.1rem 0.5rem 1rem 0.5rem;
        max-width: 98vw;
    }
} 